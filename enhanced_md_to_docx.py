#!/usr/bin/env python3
"""
Enhanced Markdown to DOCX Converter for BrainstOdoopro Field Mapping Documentation
Creates a professionally formatted Word document with improved styling.
"""

import re
from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn

def setup_document_styles(doc):
    """Setup custom styles for the document"""
    
    # Title style
    try:
        title_style = doc.styles.add_style('CustomTitle', WD_STYLE_TYPE.PARAGRAPH)
        title_style.font.name = 'Calibri'
        title_style.font.size = Pt(20)
        title_style.font.bold = True
        title_style.font.color.rgb = RGBColor(0, 51, 102)  # Dark blue
        title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
        title_style.paragraph_format.space_after = Pt(12)
    except:
        pass
    
    # Heading styles
    try:
        h2_style = doc.styles.add_style('CustomHeading2', WD_STYLE_TYPE.PARAGRAPH)
        h2_style.font.name = 'Calibri'
        h2_style.font.size = Pt(16)
        h2_style.font.bold = True
        h2_style.font.color.rgb = RGBColor(0, 51, 102)
        h2_style.paragraph_format.space_before = Pt(12)
        h2_style.paragraph_format.space_after = Pt(6)
    except:
        pass
    
    try:
        h3_style = doc.styles.add_style('CustomHeading3', WD_STYLE_TYPE.PARAGRAPH)
        h3_style.font.name = 'Calibri'
        h3_style.font.size = Pt(14)
        h3_style.font.bold = True
        h3_style.font.color.rgb = RGBColor(51, 102, 153)
        h3_style.paragraph_format.space_before = Pt(10)
        h3_style.paragraph_format.space_after = Pt(4)
    except:
        pass
    
    # Body text style
    try:
        body_style = doc.styles.add_style('CustomBody', WD_STYLE_TYPE.PARAGRAPH)
        body_style.font.name = 'Calibri'
        body_style.font.size = Pt(11)
        body_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        body_style.paragraph_format.space_after = Pt(6)
    except:
        pass

def add_cover_page(doc):
    """Add a professional cover page"""
    # Title
    title = doc.add_paragraph()
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    title_run = title.add_run('BrainstOdoopro Plugin')
    title_run.font.name = 'Calibri'
    title_run.font.size = Pt(24)
    title_run.font.bold = True
    title_run.font.color.rgb = RGBColor(0, 51, 102)
    
    # Subtitle
    subtitle = doc.add_paragraph()
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    subtitle_run = subtitle.add_run('Odoo Field Mapping Documentation')
    subtitle_run.font.name = 'Calibri'
    subtitle_run.font.size = Pt(18)
    subtitle_run.font.color.rgb = RGBColor(51, 102, 153)
    
    # Add some space
    doc.add_paragraph()
    doc.add_paragraph()
    
    # Description
    desc = doc.add_paragraph()
    desc.alignment = WD_ALIGN_PARAGRAPH.CENTER
    desc_run = desc.add_run('Complete Field Mapping Reference\nShopware 6 ↔ Odoo Integration')
    desc_run.font.name = 'Calibri'
    desc_run.font.size = Pt(14)
    desc_run.font.color.rgb = RGBColor(102, 102, 102)
    
    # Add more space
    for _ in range(10):
        doc.add_paragraph()
    
    # Footer info
    footer = doc.add_paragraph()
    footer.alignment = WD_ALIGN_PARAGRAPH.CENTER
    footer_run = footer.add_run('Generated Documentation\nVersion 1.0')
    footer_run.font.name = 'Calibri'
    footer_run.font.size = Pt(12)
    footer_run.font.color.rgb = RGBColor(128, 128, 128)
    
    # Page break
    doc.add_page_break()

def create_table_of_contents(doc, sections):
    """Create a table of contents"""
    toc_title = doc.add_heading('Table of Contents', level=1)
    toc_title.runs[0].font.color.rgb = RGBColor(0, 51, 102)
    
    toc_items = []
    
    def extract_headings(sections, level=0):
        for section in sections:
            if section['type'] == 'heading' and section['level'] <= 3:
                indent = '    ' * (section['level'] - 2) if section['level'] > 2 else ''
                toc_items.append(f"{indent}{section['text']}")
                if 'content' in section:
                    extract_headings(section['content'], level + 1)
    
    extract_headings(sections)
    
    for item in toc_items:
        para = doc.add_paragraph(item)
        para.style = 'List Number'
    
    doc.add_page_break()

def format_table(table):
    """Apply professional formatting to tables"""
    # Set table alignment
    table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Format header row
    if len(table.rows) > 0:
        header_row = table.rows[0]
        for cell in header_row.cells:
            # Set header background color (light blue)
            shading_elm = OxmlElement('w:shd')
            shading_elm.set(qn('w:fill'), 'E6F3FF')
            cell._tc.get_or_add_tcPr().append(shading_elm)
            
            # Format header text
            for paragraph in cell.paragraphs:
                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                for run in paragraph.runs:
                    run.font.bold = True
                    run.font.color.rgb = RGBColor(0, 51, 102)
    
    # Format data rows
    for i, row in enumerate(table.rows[1:], 1):
        for cell in row.cells:
            # Alternate row colors
            if i % 2 == 0:
                shading_elm = OxmlElement('w:shd')
                shading_elm.set(qn('w:fill'), 'F8F9FA')
                cell._tc.get_or_add_tcPr().append(shading_elm)
            
            # Format cell text
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    run.font.size = Pt(10)

def parse_and_convert(input_file, output_file):
    """Parse markdown and convert to formatted DOCX"""
    
    # Read markdown file
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Create document
    doc = Document()
    
    # Set up document properties
    doc.core_properties.title = "BrainstOdoopro Plugin - Odoo Field Mapping Documentation"
    doc.core_properties.author = "BrainstOdoopro Plugin Documentation"
    doc.core_properties.subject = "Shopware to Odoo Field Mapping Reference"
    
    # Set margins
    for section in doc.sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1.25)
        section.right_margin = Inches(1.25)
    
    # Setup styles
    setup_document_styles(doc)
    
    # Parse content into sections
    sections = []
    lines = content.split('\n')
    current_section = None
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        if line.startswith('# '):
            # Main title - skip for now, we'll add cover page
            pass
        elif line.startswith('## '):
            # Section header
            heading = doc.add_heading(line[3:].strip(), level=1)
            heading.runs[0].font.color.rgb = RGBColor(0, 51, 102)
            heading.runs[0].font.size = Pt(16)
        elif line.startswith('### '):
            # Subsection header
            heading = doc.add_heading(line[4:].strip(), level=2)
            heading.runs[0].font.color.rgb = RGBColor(51, 102, 153)
            heading.runs[0].font.size = Pt(14)
        elif line.startswith('#### '):
            # Sub-subsection header
            heading = doc.add_heading(line[5:].strip(), level=3)
            heading.runs[0].font.color.rgb = RGBColor(102, 153, 204)
            heading.runs[0].font.size = Pt(12)
        elif line.startswith('| ') and '|' in line:
            # Table
            table_data = []
            while i < len(lines) and lines[i].strip().startswith('|'):
                row = [cell.strip() for cell in lines[i].strip().split('|')[1:-1]]
                if not all(cell.startswith('-') for cell in row):
                    table_data.append(row)
                i += 1
            i -= 1
            
            if table_data and len(table_data) > 1:
                table = doc.add_table(rows=len(table_data), cols=len(table_data[0]))
                table.style = 'Table Grid'
                
                for row_idx, row_data in enumerate(table_data):
                    for col_idx, cell_data in enumerate(row_data):
                        if col_idx < len(table.rows[row_idx].cells):
                            table.rows[row_idx].cells[col_idx].text = cell_data
                
                format_table(table)
                doc.add_paragraph()  # Add space after table
        elif line.startswith('- ') or line.startswith('* '):
            # List
            list_items = []
            while i < len(lines) and (lines[i].strip().startswith('- ') or lines[i].strip().startswith('* ')):
                list_items.append(lines[i].strip()[2:])
                i += 1
            i -= 1
            
            for item in list_items:
                para = doc.add_paragraph(item, style='List Bullet')
                para.runs[0].font.size = Pt(11)
        elif line and not line.startswith('---'):
            # Regular paragraph
            if line.strip():
                para = doc.add_paragraph(line)
                para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
                for run in para.runs:
                    run.font.size = Pt(11)
                    run.font.name = 'Calibri'
        
        i += 1
    
    # Save document
    doc.save(output_file)
    print(f"Enhanced DOCX document created: {output_file}")

def main():
    """Main function"""
    input_file = 'BrainstOdoopro_Field_Mapping_Documentation.md'
    output_file = 'BrainstOdoopro_Field_Mapping_Documentation_Enhanced.docx'
    
    print(f"Creating enhanced DOCX from {input_file}...")
    parse_and_convert(input_file, output_file)

if __name__ == '__main__':
    main()
