# BrainstOdoopro Plugin - Odoo Field Mapping Documentation

## Overview
This document provides a comprehensive mapping of Shopware fields to Odoo fields for the BrainstOdoopro plugin. The plugin synchronizes data between Shopware 6 and Odoo across multiple modules, including all line items and detailed field mappings.

## Module Hierarchy

### Core Modules
1. **Sales Channel** (`sales_channel`)
2. **Category** (`category`)
3. **Product** (`product`)
4. **Template** (`template`)
5. **Customer** (`customer`)
6. **Customer Address** (`customer_address`)
7. **Property/Attribute** (`attribute`)
8. **Property Value** (`attribute_value`)
9. **Order** (`order`)
10. **Order Line Items** (`order_line`)
11. **Delivery** (`delivery`)
12. **Stock Moves** (`stock_move`)
13. **Transaction** (`transaction`)
14. **Invoice Lines** (`invoice_line`)
15. **Tax Management** (`tax`)
16. **Country/State** (`country_state`)
17. **Currency** (`currency`)

### Supporting Models
- **Product Attribute Lines** (`product_attribute_line`)
- **Account Payment Terms** (`payment_term`)
- **Stock Locations** (`stock_location`)
- **Unit of Measure** (`product_uom`)

---

## 1. Sales Channel Module (`sales_channel`)

**Odoo Model:** `res.company`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `name` | `name` | Sales channel name |
| `countryId` | `country_id` | Default country for the sales channel |
| `currencyId` | `currency_id` | Default currency for the sales channel |

**Custom Values Added:**
- Unique name generation to avoid conflicts in Odoo
- Company settings synchronization

---

## 2. Category Module (`category`)

**Odoo Model:** `product.category`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `name` | `name` | Category name |
| `parentId` | `parent_id` | Parent category reference |

**Custom Values Added:**
- Hierarchical category structure maintained
- Automatic parent-child relationship mapping

---

## 3. Product Module (`product`)

**Odoo Model:** `product.template` (for main products) / `product.product` (for variants)

### Main Product Fields
| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `name` | `name` | Product name |
| `price.gross` | `list_price` | Product selling price |
| `description` | `description` | Product description |
| `categoryIds[0]` | `categ_id` | Primary category assignment |
| `productNumber` | `default_code` | Product SKU/code (for variants) |

**Custom Values Added:**
- Price conversion from Shopware pricing structure
- Category mapping through CategorySyncService
- Variant handling with attribute line IDs
- Stock quantity synchronization

### Product Variants
- Variants are handled separately with `default_code` mapping
- Parent products get full field mapping
- Variant products only get product number mapping

---

## 4. Template Module (`template`)

**Odoo Model:** `product.template`

Used for Odoo-to-Shopware synchronization of product templates.

| Odoo Field | Shopware Field | Description |
|------------|----------------|-------------|
| `name` | `name` | Product template name |
| `description` | `description` | Product description |
| `list_price` | `price.net` | Net price (converted to gross) |
| `qty_available` | `stock` | Available quantity |

**Custom Values Added:**
- Auto-generated product number for new products
- Tax calculation for gross price conversion
- Default stock value of 0 for new products
- Active status set to true

---

## 5. Customer Module (`customer`)

**Odoo Model:** `res.partner`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `firstName + lastName` | `name` | Full customer name |
| `email` | `email` | Customer email address |
| `title` | `function` | Customer title/position |
| `vatIds` | `vat` | VAT identification number |
| `defaultBillingAddress.street` | `street` | Primary street address |
| `defaultBillingAddress.additionalAddressLine1` | `street2` | Additional address line |
| `defaultBillingAddress.city` | `city` | City |
| `defaultBillingAddress.zipcode` | `zip` | Postal code |
| `defaultBillingAddress.countryId` | `country_id` | Country reference |
| `defaultBillingAddress.countryStateId` | `state_id` | State/province reference |
| `defaultBillingAddress.phoneNumber` | `phone` | Phone number |

**Custom Values Added:**
- Name splitting logic for firstName/lastName
- Account type mapping (business/private)
- Default customer group assignment
- Default sales channel assignment
- Default payment method assignment
- Auto-generated customer number
- Default password for new customers

---

## 6. Customer Address Module (`customer_address`)

**Odoo Model:** `res.partner` (child records)

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `street` | `street` | Street address |
| `additionalAddressLine1` | `street2` | Additional address line |
| `city` | `city` | City |
| `zipcode` | `zip` | Postal code |
| `countryId` | `country_id` | Country reference |
| `countryStateId` | `state_id` | State/province reference |
| `phoneNumber` | `phone` | Phone number |

**Custom Values Added:**
- Parent customer relationship maintained
- Address type handling (billing/shipping)

---

## 7. Property/Attribute Module (`attribute`)

**Odoo Model:** `product.attribute`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `name` | `name` | Attribute name |
| `displayType` | `display_type` | Display type (converted: media/text → pills) |

**Custom Values Added:**
- Display type conversion logic
- Filterable property set to true
- Sorting type set to alphanumeric

---

## 8. Property Value Module (`attribute_value`)

**Odoo Model:** `product.attribute.value`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `name` | `name` | Attribute value name |
| `propertyGroupId` | `attribute_id` | Parent attribute reference |

**Custom Values Added:**
- Automatic parent attribute relationship
- Value synchronization with parent attribute

---

## 9. Order Module (`order`)

**Odoo Model:** `sale.order`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `customerId` | `partner_id` | Customer reference |
| `billingAddressId` | `partner_invoice_id` | Billing address reference |
| `shippingAddressId` | `partner_shipping_id` | Shipping address reference |
| `stateMachineState` | `state` | Order status |
| `amountNet` | `amount_untaxed` | Order net amount |
| `positionPrice - amountNet` | `amount_tax` | Tax amount |
| `amountTotal` | `amount_total` | Total order amount |
| `salesChannelId` | `company_id` | Company reference |
| Order date | `date_order` | Order creation date |
| - | `order_line` | Order line items (One2many) |
| - | `currency_id` | Order currency |
| - | `payment_term_id` | Payment terms |

**Custom Values Added:**
- Order status mapping between Shopware and Odoo states
- Customer and address relationship handling
- Order line items synchronization
- Automatic date formatting (Y-m-d)
- Payment term assignment from first available term
- Currency mapping through sales channel

---

## 10. Order Line Items Module (`order_line`)

**Odoo Model:** `sale.order.line`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| Line item name | `name` | Product/service name |
| `productId` | `product_id` | Product reference |
| `quantity` | `product_uom_qty` | Ordered quantity |
| `unitPrice` | `price_unit` | Unit price |
| `totalPrice` | `price_total` | Line total price |
| Tax information | `tax_ids` | Applied taxes (Many2many) |
| Order reference | `order_id` | Parent order reference |
| - | `sale_line_ids` | Related sale line IDs |

**Line Item Types:**
- **Product Lines:** Regular product items with full field mapping
- **Shipping Lines:** Special "Shipping Charge" line items
- **Custom Products:** Dynamically created products for services

**Custom Values Added:**
- Automatic tax calculation and assignment
- Product creation for non-existing items
- Shipping charge handling as separate line item
- Line item update/delete operations via Odoo commands (0,0,{data}), (2,id,0)

---

## 11. Delivery Module (`delivery`)

**Odoo Model:** `stock.picking`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `stateMachineState` | `state` | Delivery status |
| Order reference | `sale_id` | Related sales order |
| Shipping address | `partner_id` | Delivery partner |
| Order number | `origin` | Source reference |
| - | `picking_type_id` | Picking type (outgoing) |
| - | `company_id` | Company reference |
| - | `move_ids_without_package` | Stock moves (One2many) |

**Custom Values Added:**
- Delivery action mapping (confirm, validate, cancel)
- Stock move management
- Delivery type assignment (outgoing)
- Move line items creation
- Automatic picking type detection

---

## 12. Stock Moves Module (`stock_move`)

**Odoo Model:** `stock.move`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| Product reference | `product_id` | Product being moved |
| Line item name | `name` | Move description |
| Quantity | `product_uom_qty` | Quantity to move |
| - | `picking_id` | Parent delivery reference |
| - | `sale_line_id` | Related sale order line |
| - | `state` | Move state (draft, confirmed, done) |
| - | `company_id` | Company reference |
| - | `location_id` | Source location |
| - | `location_dest_id` | Destination location |

**Custom Values Added:**
- Automatic state management (draft → confirmed → done)
- Sale line relationship mapping
- Location assignment based on picking type
- Move line creation for detailed tracking

---

## 13. Transaction Module (`transaction`)

**Odoo Model:** `account.move` (invoices) / `account.payment`

### Invoice Fields (`account.move`)
| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `stateMachineState` | `payment_state` | Payment status |
| Order reference | `invoice_origin` | Source order reference |
| Customer | `partner_id` | Invoice partner |
| Billing address | `partner_invoice_id` | Invoice address |
| Shipping address | `partner_shipping_id` | Shipping address |
| - | `move_type` | Invoice type (out_invoice) |
| - | `state` | Invoice state (draft, posted, cancel) |
| - | `company_id` | Company reference |
| - | `currency_id` | Invoice currency |
| - | `invoice_line_ids` | Invoice line items (One2many) |

**Custom Values Added:**
- Payment action mapping (paid, cancel, refund)
- Invoice state management (draft, posted, cancel)
- Payment validation and processing
- Version-specific handling (v17 vs older versions)
- Automatic invoice line creation from order lines

---

## 14. Invoice Lines Module (`invoice_line`)

**Odoo Model:** `account.move.line`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| Line item name | `name` | Product/service description |
| `productId` | `product_id` | Product reference |
| `quantity` | `quantity` | Invoiced quantity |
| `unitPrice` | `price_unit` | Unit price |
| `totalPrice` | `price_total` | Line total |
| Tax information | `tax_ids` | Applied taxes |
| - | `sale_line_ids` | Related sale order lines |
| - | `move_id` | Parent invoice reference |
| - | `account_id` | Account for posting |

**Custom Values Added:**
- Automatic account assignment based on product
- Tax calculation and assignment
- Sale line relationship mapping
- Line-level discount handling

---

## 15. Tax Management Module (`tax`)

**Odoo Model:** `account.tax` / `account.tax.group`

### Tax Fields (`account.tax`)
| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| Tax rate | `amount` | Tax percentage |
| Tax name | `name` | Tax description (e.g., "Shopware 19%") |
| - | `amount_type` | Tax type (percent) |
| - | `type_tax_use` | Usage type (sale) |
| - | `company_id` | Company reference |
| - | `tax_group_id` | Tax group reference |
| - | `active` | Tax status |
| - | `invoice_repartition_line_ids` | Invoice tax lines |
| - | `refund_repartition_line_ids` | Refund tax lines |

### Tax Group Fields (`account.tax.group`)
| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| Tax rate | `name` | Group name (e.g., "Shopware Tax 19%") |
| - | `company_id` | Company reference |
| - | `tax_payable_account_id` | Payable account |
| - | `tax_receivable_account_id` | Receivable account |

**Custom Values Added:**
- Dynamic tax creation based on Shopware rates
- Tax group automatic assignment
- Account assignment for tax posting
- Repartition line creation for tax distribution

---

## 16. Country/State Module (`country_state`)

**Odoo Models:** `res.country` / `res.country.state`

### Country Fields (`res.country`)
| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `iso` | `code` | Country ISO code |
| `name` | `name` | Country name |
| `postalCodeRequired` | `zip_required` | Postal code requirement |
| `forceStateInRegistration` | `state_requires` | State requirement |

### State Fields (`res.country.state`)
| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `shortCode` | `code` | State code |
| `name` | `name` | State name |
| Country reference | `country_id` | Parent country |

**Custom Values Added:**
- Automatic country creation if not exists
- State code normalization (removes country prefix)
- Country-state relationship validation

---

## 17. Currency Module (`currency`)

**Odoo Models:** `res.currency` / `product.pricelist`

### Currency Fields (`res.currency`)
| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `isoCode` | `name` | Currency code (EUR, USD, etc.) |
| `name` | `full_name` | Currency full name |
| `symbol` | `symbol` | Currency symbol |
| `itemRounding.decimals` | `rounding` | Decimal precision |
| - | `position` | Symbol position (after) |
| - | `active` | Currency status |
| - | `rate_ids` | Exchange rates |

### Pricelist Fields (`product.pricelist`)
| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| Currency code | `name` | Pricelist name |
| - | `currency_id` | Currency reference |
| - | `company_id` | Company reference |

**Custom Values Added:**
- Automatic currency activation
- Exchange rate creation
- Pricelist generation per currency
- Currency rate management

---

## 18. Product Attribute Lines Module (`product_attribute_line`)

**Odoo Model:** `product.template.attribute.line`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| Property group | `attribute_id` | Attribute reference |
| Property values | `value_ids` | Attribute values (Many2many) |
| - | `product_tmpl_id` | Product template reference |

**Custom Values Added:**
- Automatic attribute line creation for variants
- Value synchronization with Shopware properties
- Line update/delete operations for variant changes

---

## 19. Supporting Models

### Account Payment Terms (`account.payment.term`)
| Odoo Field | Description |
|------------|-------------|
| `name` | Payment term name |
| `company_id` | Company reference |

### Stock Locations (`stock.location`)
| Odoo Field | Description |
|------------|-------------|
| `name` | Location name |
| `usage` | Location type (internal, customer, supplier) |
| `company_id` | Company reference |

### Unit of Measure (`product.uom`)
| Odoo Field | Description |
|------------|-------------|
| `name` | UoM name |
| `category_id` | UoM category |
| `factor` | Conversion factor |

---

## Field Automation Features

The plugin includes automation for the following Odoo fields:

### Core Entity Fields
- **Category fields:** `parent_id`, `name`
- **Product fields:** `list_price`, `categ_id`, `description`, `qty_available`, `attribute_line_ids`, `default_code`, `standard_price`
- **Property fields:** `active`, `value_ids`, `display_type`
- **Customer fields:** `email`, `function`, `vat`, `street`, `street2`, `city`, `zip`, `country_id`, `state_id`, `phone`, `title` (v17)

### Line Item Fields
- **Order line fields:** `name`, `product_id`, `product_uom_qty`, `price_unit`, `price_total`, `tax_ids`
- **Invoice line fields:** `sale_line_ids`, `quantity`, `account_id`
- **Stock move fields:** `sale_line_id`, `location_id`, `location_dest_id`, `state`
- **Attribute line fields:** `attribute_id`, `value_ids`, `product_tmpl_id`

### System Fields
- **Tax fields:** `amount`, `amount_type`, `type_tax_use`, `tax_group_id`, `invoice_repartition_line_ids`
- **Currency fields:** `name`, `symbol`, `rounding`, `rate_ids`, `active`
- **Country/State fields:** `code`, `zip_required`, `state_requires`

## Field Types and Relationships

### Odoo Field Types Used
- **Char/Text:** `name`, `description`, `street`, `email`, `phone`
- **Integer:** `odoo_id`, `company_id`, `partner_id`, `product_id`
- **Float:** `list_price`, `amount`, `quantity`, `price_unit`, `rounding`
- **Boolean:** `active`, `zip_required`, `state_requires`
- **Selection:** `state`, `move_type`, `amount_type`, `type_tax_use`
- **Date/Datetime:** `date_order`, `create_date`, `write_date`

### Relationship Fields
- **Many2one:** `partner_id`, `country_id`, `categ_id`, `currency_id`, `company_id`
- **One2many:** `order_line`, `invoice_line_ids`, `move_ids_without_package`, `attribute_line_ids`
- **Many2many:** `tax_ids`, `value_ids`, `sale_line_ids`

### Computed Fields
- **Product:** `qty_available` (computed from stock moves)
- **Order:** `amount_untaxed`, `amount_tax`, `amount_total` (computed from lines)
- **Invoice:** `payment_state` (computed from payments)

## Synchronization Direction

- **Shopware → Odoo:** All modules support this direction
- **Odoo → Shopware:** Supported for products, customers, categories, and properties
- **Bidirectional:** Full synchronization with conflict resolution

## Version-Specific Differences

### Odoo v17 vs Older Versions
- **Customer title field:** v17 uses `title`, older versions use `function`
- **Payment processing:** Different API methods for payment validation
- **Invoice creation:** Enhanced invoice line handling in v17
- **Tax repartition:** Improved tax line management in v17

## Notes

1. **Complex Transformations:** Price calculations include tax conversion, currency handling
2. **Relationship Management:** Parent-child relationships are maintained across both systems
3. **Data Validation:** Field validation and error handling for data integrity
4. **Batch Processing:** Bulk operations supported for performance optimization
5. **Version Compatibility:** Handles different Odoo versions (v17 and older)
6. **Line Item Management:** Supports create (0,0,data), update (1,id,data), delete (2,id,0) operations
7. **Dynamic Field Creation:** Automatically creates missing taxes, countries, currencies as needed
8. **State Management:** Proper state transitions for orders, deliveries, and invoices

---

## Complete Odoo Models and Fields Summary

### Core Business Models

#### 1. `res.company` (Sales Channel)
- `name`, `country_id`, `currency_id`

#### 2. `product.category` (Category)
- `name`, `parent_id`

#### 3. `product.template` (Product Template)
- `name`, `list_price`, `description`, `categ_id`, `qty_available`, `attribute_line_ids`, `standard_price`, `active`

#### 4. `product.product` (Product Variant)
- `default_code`, `product_tmpl_id`, `product_variant_ids`

#### 5. `res.partner` (Customer/Address)
- `name`, `email`, `street`, `street2`, `city`, `zip`, `country_id`, `state_id`, `phone`, `function`, `vat`, `title` (v17), `parent_id`, `is_company`

#### 6. `product.attribute` (Property/Attribute)
- `name`, `display_type`, `active`, `value_ids`

#### 7. `product.attribute.value` (Property Value)
- `name`, `attribute_id`

#### 8. `sale.order` (Order)
- `partner_id`, `partner_invoice_id`, `partner_shipping_id`, `state`, `amount_untaxed`, `amount_tax`, `amount_total`, `company_id`, `date_order`, `order_line`, `currency_id`, `payment_term_id`, `name`, `picking_ids`, `amount_to_invoice`, `delivery_status`

#### 9. `sale.order.line` (Order Line Items)
- `name`, `product_id`, `product_uom_qty`, `price_unit`, `price_total`, `tax_ids`, `order_id`, `sale_line_ids`

### Inventory & Delivery Models

#### 10. `stock.picking` (Delivery)
- `state`, `sale_id`, `partner_id`, `origin`, `picking_type_id`, `company_id`, `move_ids_without_package`

#### 11. `stock.move` (Stock Moves)
- `product_id`, `name`, `product_uom_qty`, `picking_id`, `sale_line_id`, `state`, `company_id`, `location_id`, `location_dest_id`

#### 12. `stock.location` (Stock Locations)
- `name`, `usage`, `company_id`

### Accounting Models

#### 13. `account.move` (Invoice)
- `payment_state`, `invoice_origin`, `partner_id`, `partner_invoice_id`, `partner_shipping_id`, `move_type`, `state`, `company_id`, `currency_id`, `invoice_line_ids`

#### 14. `account.move.line` (Invoice Lines)
- `name`, `product_id`, `quantity`, `price_unit`, `price_total`, `tax_ids`, `sale_line_ids`, `move_id`, `account_id`

#### 15. `account.payment` (Payments)
- `payment_state`, `amount`, `currency_id`, `partner_id`

#### 16. `account.tax` (Taxes)
- `amount`, `name`, `amount_type`, `type_tax_use`, `company_id`, `tax_group_id`, `active`, `invoice_repartition_line_ids`, `refund_repartition_line_ids`, `description`, `invoice_label`, `cash_basis_transition_account_id`, `include_base_amount`, `is_base_affected`, `name_searchable`, `price_include_override`, `tax_exigibility`, `tax_scope`

#### 17. `account.tax.group` (Tax Groups)
- `name`, `company_id`, `tax_payable_account_id`, `tax_receivable_account_id`

#### 18. `account.payment.term` (Payment Terms)
- `name`, `company_id`

### Geographic Models

#### 19. `res.country` (Countries)
- `name`, `code`, `zip_required`, `state_requires`

#### 20. `res.country.state` (States)
- `name`, `code`, `country_id`

### Currency & Pricing Models

#### 21. `res.currency` (Currencies)
- `name`, `full_name`, `symbol`, `rounding`, `position`, `active`, `rate_ids`

#### 22. `product.pricelist` (Pricelists)
- `name`, `currency_id`, `company_id`

### Product Attribute Models

#### 23. `product.template.attribute.line` (Product Attribute Lines)
- `attribute_id`, `value_ids`, `product_tmpl_id`

#### 24. `product.uom` (Unit of Measure)
- `name`, `category_id`, `factor`

### System Models

#### 25. `ir.model` (Model Metadata)
- `id`, `model`

#### 26. `ir.model.fields` (Field Metadata)
- `id`, `model`, `name`

---

## Total Field Count by Model

| Model | Field Count | Primary Use |
|-------|-------------|-------------|
| `res.partner` | 15+ | Customer/Address management |
| `sale.order` | 14+ | Order processing |
| `account.tax` | 13+ | Tax management |
| `product.template` | 8+ | Product catalog |
| `sale.order.line` | 8+ | Order line items |
| `account.move` | 8+ | Invoice management |
| `stock.move` | 9+ | Inventory movements |
| `account.move.line` | 8+ | Invoice line items |
| `res.currency` | 7+ | Currency management |
| `stock.picking` | 7+ | Delivery management |
| `product.attribute` | 4+ | Product attributes |
| `res.country` | 4+ | Geographic data |
| `account.tax.group` | 4+ | Tax grouping |
| Others | 3- | Supporting functions |

**Total Estimated Fields: 150+ unique Odoo fields across 26+ models**
