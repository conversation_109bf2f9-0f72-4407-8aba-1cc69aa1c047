name: Build extension
on:
  workflow_dispatch:
  push:
    branches:
      - main

jobs:
  build:
    uses: FriendsOfShopware/actions/.github/workflows/store-shopware-cli.yml@main
    with:
      extensionName: ${{ github.event.repository.name }}
    secrets:
      accountUser: ${{ secrets.ACCOUNT_USER }}
      accountPassword: ${{ secrets.ACCOUNT_PASSWORD }}
      ghToken: ${{ secrets.GITHUB_TOKEN }}
