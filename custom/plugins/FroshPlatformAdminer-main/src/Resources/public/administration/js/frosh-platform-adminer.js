(function(){var e={354:function(){Shopware.Service("privileges").addPrivilegeMappingEntry({category:"additional_permissions",parent:null,key:"system",roles:{frosh_adminer:{privileges:[],dependencies:[]}}})}},r={};function t(i){var n=r[i];if(void 0!==n)return n.exports;var a=r[i]={exports:{}};return e[i](a,a.exports,t),a.exports}t.p="bundles/froshplatformadminer/",window?.__sw__?.assetPath&&(t.p=window.__sw__.assetPath+"/bundles/froshplatformadminer/"),function(){"use strict";let e=Shopware.Classes.ApiService;var r=class extends e{constructor(e,r,t="frosh_adminer"){super(e,r,t)}loginToAdminer(){let r=`${this.getApiBasePath()}/login`;return this.httpClient.get(r,{headers:this.getBasicHeaders()}).then(r=>e.handleResponse(r))}};let{Application:i}=Shopware;i.addServiceProvider("AdminerService",e=>new r(i.getContainer("init").httpClient,e.loginService)),t(354);let{Component:n}=Shopware;n.register("frosh-adminer-view",{template:'<sw-page class="adminer">\n    <template #smart-bar-header>\n        <h2>Adminer</h2>\n    </template>\n\n    <template #content>\n        <iframe v-if="apiAdminerUrl" :src="apiAdminerUrl" width="100%" height="99%" frameborder="0"></iframe>\n    </template>\n\n    <template #smart-bar-actions>\n        <sw-button variant="primary" v-if="apiAdminerUrl" @click="openNewTab">\n            {{ $tc(\'frosh-adminer.openInNewTab\') }}\n        </sw-button>\n    </template>\n</sw-page>\n',inject:["AdminerService"],created(){this.createdComponent()},data(){return{apiAdminerUrl:!1}},methods:{createdComponent(){this.AdminerService.loginToAdminer().then(e=>{this.apiAdminerUrl=e.url})},openNewTab(){window.open(this.apiAdminerUrl)}}});let{Module:a}=Shopware;a.register("frosh-adminer",{type:"plugin",name:"frosh-adminer.title",description:"frosh-adminer.title",color:"#9AA8B5",icon:"regular-database",favicon:"icon-module-settings.png",routes:{list:{component:"frosh-adminer-view",path:"list",privilege:"system.frosh_adminer"}},settingsItem:[{group:"plugins",to:"frosh.adminer.list",icon:"regular-database",label:"frosh-adminer.title",privilege:"system.frosh_adminer"}],extensionEntryRoute:{extensionName:"FroshPlatformAdminer",route:"frosh.adminer.list"}})}()})();