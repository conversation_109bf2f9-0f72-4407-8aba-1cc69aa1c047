<?php

/** Allow using <PERSON><PERSON><PERSON> inside a frame (disables ClickJacking protection)
 * @link https://www.adminer.org/plugins/#use
 * <AUTHOR> https://www.vrana.cz/
 * @license https://www.apache.org/licenses/LICENSE-2.0 Apache License, Version 2.0
 * @license https://www.gnu.org/licenses/gpl-2.0.html GNU General Public License, version 2 (one or other)
 */
class AdminerFrames {
    /** @access protected */
    var $sameOrigin;

    /**
     * @param bool allow running from the same origin only
     */
    function __construct($sameOrigin = false) {
        $this->sameOrigin = $sameOrigin;
    }

    function headers() {
        if ($this->sameOrigin) {
            header("X-Frame-Options: SameOrigin");
        } elseif (function_exists('header_remove')) {
            header_remove("X-Frame-Options");
        }
    }

}
