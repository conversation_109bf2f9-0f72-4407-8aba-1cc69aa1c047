store:
    availabilities:
        - German
        - International
    default_locale: en_GB
    localizations:
        - de_DE
        - en_GB
    categories:
        - System
    type: extension
    icon: src/Resources/store/icon.png
    automatic_bugfix_version_compatibility: true
    description:
        de: |
            <p>Adminer erlaubt es dir einfach in der Administration auf die Datenbank zuzugreifen.</p>
            <p>Das Plugin wird von der Github Organization <a rel="noopener noreferrer" target="_blank" href="https://github.com/FriendsOfShopware/">FriendsOfShopware</a> entwickelt.
            Maintainer des Plugin ist <a rel="noopener noreferrer" target="_blank" href="https://github.com/shyim">Soner Sayakci</a>.
            Das Github Repository ist zu finden <a rel="noopener noreferrer" target="_blank" href="https://github.com/FriendsOfShopware/FroshPlatformAdminer">hier</a>
            [Bei Fragen / Fehlern bitte ein Github Issue erstellen](https://github.com/FriendsOfShopware/FroshPlatformAdminer/issues/new</p>
        en: |
            <p>Adminer allows you to easily access the database in the administration.</p>
            <p>The plugin is provided by the Github Organization <a rel="noopener noreferrer" target="_blank" href="https://github.com/FriendsOfShopware/">FriendsOfShopware</a>.
            Maintainer of the plugin is <a rel="noopener noreferrer" target="_blank" href="https://github.com/shyim">Soner Sayakci</a>.
            You can find the Github Repository <a rel="noopener noreferrer" target="_blank" href="https://github.com/FriendsOfShopware/FroshPlatformAdminer">here</a>
            <a rel="noopener noreferrer" target="_blank" href="https://github.com/FriendsOfShopware/FroshPlatformAdminer/issues/new">For questions / errors please create a Github Issue</a></p>
    installation_manual:
        de: ""
        en: ""
    tags:
        de:
            - Datenbank
            - adminer
        en:
            - database
            - adminer
    videos:
        de: []
        en: []
    highlights:
        de:
            - ""
        en:
            - ""
    features:
        de:
            - ""
        en:
            - ""
    faq:
        de: []
        en: []
    images:
        - file: src/Resources/store/img-0.png
          activate:
            de: true
            en: true
          preview:
            de: true
            en: true
          priority: 0
build:
    zip:
        assets:
            enable_es_build_for_admin: true
            enable_es_build_for_storefront: true

changelog:
  enabled: true
