{% block brainst_odoo_pro_automation_modal %}
    <sw-modal
            class="brainst-odoo-pro-automation-modal"
            :title="$tc('brainst-odoo-pro.automation-modal.title')"
            variant="large"
            @modal-close="onCloseModal"
    >
        {% block brainst_odoo_pro_automation_modal_body %}
            {% block brainst_odoo_pro_automation_modal_content %}
                <div class="brainst-odoo-pro-automation-modal__content">
                    {% block brainst_odoo_pro_automation_modal_description %}
                        <p class="brainst-odoo-pro-automation-modal__description">
                            {{ $tc('brainst-odoo-pro.automation-modal.description') }}
                        </p>
                    {% endblock %}

                    {% block brainst_odoo_pro_automation_modal_modules %}
                        <div class="brainst-odoo-pro-automation-modal__modules-section">
                            <h3 class="brainst-odoo-pro-automation-modal__section-title">
                                {{ $tc('brainst-odoo-pro.automation-modal.modulesTitle') }}
                            </h3>
                            <p class="brainst-odoo-pro-automation-modal__section-description">
                                {{ $tc('brainst-odoo-pro.automation-modal.modulesDescription') }}
                            </p>

                            <div class="brainst-odoo-pro-automation-modal__modules-grid">
                                <div class="brainst-odoo-pro-automation-modal__module-card" v-for="module in availableModules" :key="module.key">
                                    <div class="brainst-odoo-pro-automation-modal__module-header">
                                        <sw-icon :name="module.icon" size="20px" class="brainst-odoo-pro-automation-modal__module-icon"></sw-icon>
                                        <h4 class="brainst-odoo-pro-automation-modal__module-title">{{ $tc(module.titleKey) }}</h4>
                                    </div>

                                    <div class="brainst-odoo-pro-automation-modal__module-toggles">
                                        <sw-switch-field
                                                class="brainst-odoo-pro-automation-modal__module-toggle"
                                                :label="$tc('brainst-odoo-pro.automation-modal.shopwareToOdoo')"
                                                v-model:value="moduleConfigs[module.key].shopwareToOdoo"
                                                size="small"
                                        />
                                        <sw-switch-field
                                                v-if="shouldShowOdooToShopware(module)"
                                                class="brainst-odoo-pro-automation-modal__module-toggle"
                                                :label="$tc('brainst-odoo-pro.automation-modal.odooToShopware')"
                                                v-model:value="moduleConfigs[module.key].odooToShopware"
                                                size="small"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endblock %}

                    {% block brainst_odoo_pro_automation_modal_toggle %}
                        <div class="brainst-odoo-pro-automation-modal__toggle-container">
                            <sw-switch-field
                                    class="brainst-odoo-pro-automation-modal__toggle"
                                    :label="$tc('brainst-odoo-pro.automation-modal.toggleLabel')"
                                    v-model:value="enableTwoWaySync"
                                    :helpText="$tc('brainst-odoo-pro.automation-modal.toggleHelpText')"
                            />
                        </div>
                    {% endblock %}

                    {% block brainst_odoo_pro_automation_modal_date_field %}
                        <div class="brainst-odoo-pro-automation-modal__date-field">
                            <sw-datepicker
                                    class="brainst-odoo-pro-automation-modal__date-picker"
                                    :label="$tc('brainst-odoo-pro.automation-modal.syncOrderFromDate.label')"
                                    :helpText="$tc('brainst-odoo-pro.automation-modal.syncOrderFromDate.helpText')"
                                    :placeholder="$tc('brainst-odoo-pro.automation-modal.syncOrderFromDate.placeholder')"
                                    v-model:value="syncOrderFromDate"
                                    dateType="date"
                            />
                        </div>
                    {% endblock %}
                </div>
            {% endblock %}
        {% endblock %}

        {% block brainst_odoo_pro_automation_modal_footer %}
            <template #modal-footer>
                {% block brainst_odoo_pro_automation_modal_footer_cancel %}
                    <sw-button
                            size="small"
                            @click="onCloseModal"
                            :disabled="isLoading"
                    >
                        {{ $tc('global.default.cancel') }}
                    </sw-button>
                {% endblock %}

                {% block brainst_odoo_pro_automation_modal_footer_save %}
                    <sw-button-process
                            class="brainst-odoo-pro-automation-modal__save-button"
                            :isLoading="isLoading"
                            variant="primary"
                            size="small"
                            @click="onSave"
                    >
                        {{ $tc('brainst-odoo-pro.automation-modal.saveButton') }}
                    </sw-button-process>
                {% endblock %}
            </template>
        {% endblock %}
    </sw-modal>
{% endblock %}
