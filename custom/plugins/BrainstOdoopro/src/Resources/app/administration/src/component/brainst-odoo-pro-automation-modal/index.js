const {Component, Mixin} = Shopware;
import template from './brainst-odoo-pro-automation-modal.html.twig';
import './brainst-odoo-pro-automation-modal.scss';

Component.register('brainst-odoo-pro-automation-modal', {
    template: template,

    inject: ['brainstOdooProApiService', 'systemConfigApiService'],

    mixins: [
        Mixin.getByName('notification')
    ],

    data() {
        return {
            isLoading: false,
            enableTwoWaySync: false,
            syncOrderFromDate: null,
            moduleConfigs: {
                salesChannels: {
                    shopwareToOdoo: true,
                    odooToShopware: false // Not supported
                },
                categories: {
                    shopwareToOdoo: true,
                    odooToShopware: false
                },
                customers: {
                    shopwareToOdoo: true,
                    odooToShopware: false
                },
                properties: {
                    shopwareToOdoo: true,
                    odooToShopware: false
                },
                products: {
                    shopwareToOdoo: true,
                    odooToShopware: false
                },
                orders: {
                    shopwareToOdoo: true,
                    odooToShopware: false // Not supported
                }
            }
        };
    },

    computed: {
        defaultSyncOrderFromDate() {
            const currentDate = new Date();
            const previousYear = currentDate.getFullYear() - 1;
            return new Date(previousYear, 0, 1); // January 1st of previous year
        },

        availableModules() {
            return [
                {
                    key: 'salesChannels',
                    titleKey: 'brainst-odoo-pro.automation-modal.modules.salesChannels',
                    icon: 'regular-storefront',
                    supportsOdooToShopware: false
                },
                {
                    key: 'categories',
                    titleKey: 'brainst-odoo-pro.automation-modal.modules.categories',
                    icon: 'regular-folder-tree',
                    supportsOdooToShopware: true
                },
                {
                    key: 'customers',
                    titleKey: 'brainst-odoo-pro.automation-modal.modules.customers',
                    icon: 'regular-users',
                    supportsOdooToShopware: true
                },
                {
                    key: 'properties',
                    titleKey: 'brainst-odoo-pro.automation-modal.modules.properties',
                    icon: 'regular-tag',
                    supportsOdooToShopware: true
                },
                {
                    key: 'products',
                    titleKey: 'brainst-odoo-pro.automation-modal.modules.products',
                    icon: 'regular-products',
                    supportsOdooToShopware: true
                },
                {
                    key: 'orders',
                    titleKey: 'brainst-odoo-pro.automation-modal.modules.orders',
                    icon: 'regular-shopping-cart',
                    supportsOdooToShopware: false
                }
            ];
        }
    },

    created() {
        // Load existing configuration or use default
        const configuredDate = this.pluginConfig?.['BrainstOdooPro.config.syncOrderFromDate'];
        if (configuredDate) {
            this.syncOrderFromDate = new Date(configuredDate);
        } else {
            this.syncOrderFromDate = this.defaultSyncOrderFromDate;
        }

        // Load existing two-way sync setting
        this.enableTwoWaySync = this.pluginConfig?.['BrainstOdooPro.config.enableTwoWaySync'] || false;

        // Load existing module configurations
        this.availableModules.forEach(module => {
            const shopwareToOdooKey = `BrainstOdooPro.config.modules.${module.key}.shopwareToOdoo`;
            const odooToShopwareKey = `BrainstOdooPro.config.modules.${module.key}.odooToShopware`;

            this.moduleConfigs[module.key].shopwareToOdoo = this.pluginConfig?.[shopwareToOdooKey] !== undefined
                ? this.pluginConfig[shopwareToOdooKey]
                : this.moduleConfigs[module.key].shopwareToOdoo;

            // Only load odooToShopware config for modules that support it
            if (module.supportsOdooToShopware) {
                this.moduleConfigs[module.key].odooToShopware = this.pluginConfig?.[odooToShopwareKey] !== undefined
                    ? this.pluginConfig[odooToShopwareKey]
                    : this.moduleConfigs[module.key].odooToShopware;
            } else {
                // Force disable for modules that don't support it
                this.moduleConfigs[module.key].odooToShopware = false;
            }
        });
    },

    props: {
        pluginConfig: {
            type: Object,
            required: true
        }
    },

    methods: {
        onCloseModal() {
            this.$emit('modal-close');
        },

        shouldShowOdooToShopware(module) {
            return this.enableTwoWaySync && module.supportsOdooToShopware;
        },

        savePluginConfig() {
            // Save the plugin configuration using system config service
            const configData = {};
            Object.keys(this.pluginConfig).forEach(key => {
                if (key.startsWith('BrainstOdooPro.config.')) {
                    configData[key] = this.pluginConfig[key];
                }
            });

            return this.systemConfigApiService.saveValues(configData);
        },

        onSave() {
            this.isLoading = true;

            this.pluginConfig['BrainstOdooPro.config.enableTwoWaySync'] = this.enableTwoWaySync;

            // Store the sync order from date in plugin config
            if (this.syncOrderFromDate) {
                this.pluginConfig['BrainstOdooPro.config.syncOrderFromDate'] = this.syncOrderFromDate.toISOString().split('T')[0];
            }

            // Store module configurations
            this.availableModules.forEach(module => {
                const shopwareToOdooKey = `BrainstOdooPro.config.modules.${module.key}.shopwareToOdoo`;
                const odooToShopwareKey = `BrainstOdooPro.config.modules.${module.key}.odooToShopware`;

                this.pluginConfig[shopwareToOdooKey] = this.moduleConfigs[module.key].shopwareToOdoo;

                // Only save odooToShopware config for modules that support it
                if (module.supportsOdooToShopware) {
                    this.pluginConfig[odooToShopwareKey] = this.moduleConfigs[module.key].odooToShopware;
                } else {
                    // Ensure it's always false for unsupported modules
                    this.pluginConfig[odooToShopwareKey] = false;
                }
            });

            // Always remove existing automations first
            this.brainstOdooProApiService
                .removeAutomation()
                .then((response) => {
                    // Save config using system config service
                    return this.savePluginConfig();
                })
                .then((response) => {
                    if (this.enableTwoWaySync) {
                        return this.brainstOdooProApiService.createAutomation();
                    } else {
                        // If two-way sync is disabled, just show success and close
                        this.createNotificationSuccess({
                            title: this.$tc('brainst-odoo-pro.automation-modal.title'),
                            message: this.$tc('brainst-odoo-pro.automation-modal.success')
                        });
                        this.onCloseModal();
                        return Promise.resolve();
                    }
                })
                .then((response) => {
                    // This will only run if enableTwoWaySync is true
                    if (this.enableTwoWaySync && response) {
                        this.createNotificationSuccess({
                            title: this.$tc('brainst-odoo-pro.automation-modal.title'),
                            message: this.$tc('brainst-odoo-pro.automation-modal.success')
                        });
                        this.onCloseModal();
                    }
                })
                .catch((error) => {
                    let errorMessage = this.$tc('brainst-odoo-pro.automation-modal.error');

                    // Try to extract error message from response
                    if (error.response && error.response.data && error.response.data.message) {
                        errorMessage = error.response.data.message;
                    } else if (error.message) {
                        errorMessage = error.message;
                    }

                    this.createNotificationError({
                        title: this.$tc('brainst-odoo-pro.automation-modal.title'),
                        message: errorMessage
                    });
                })
                .finally(() => {
                    this.isLoading = false;
                });
        }
    }
});
