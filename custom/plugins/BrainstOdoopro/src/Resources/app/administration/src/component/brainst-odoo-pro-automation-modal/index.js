const {Component, Mixin} = Shopware;
import template from './brainst-odoo-pro-automation-modal.html.twig';
import './brainst-odoo-pro-automation-modal.scss';

Component.register('brainst-odoo-pro-automation-modal', {
    template: template,

    inject: ['brainstOdooProApiService', 'systemConfigApiService'],

    mixins: [
        Mixin.getByName('notification')
    ],

    data() {
        return {
            isLoading: false,
            enableTwoWaySync: false,
            syncOrderFromDate: null,
        };
    },

    computed: {
        defaultSyncOrderFromDate() {
            const currentDate = new Date();
            const previousYear = currentDate.getFullYear() - 1;
            return new Date(previousYear, 0, 1); // January 1st of previous year
        }
    },

    created() {
        // Load existing configuration or use default
        const configuredDate = this.pluginConfig?.['BrainstOdooPro.config.syncOrderFromDate'];
        if (configuredDate) {
            this.syncOrderFromDate = new Date(configuredDate);
        } else {
            this.syncOrderFromDate = this.defaultSyncOrderFromDate;
        }

        // Load existing two-way sync setting
        this.enableTwoWaySync = this.pluginConfig?.['BrainstOdooPro.config.enableTwoWaySync'] || false;
    },

    props: {
        pluginConfig: {
            type: Object,
            required: true
        }
    },

    methods: {
        onCloseModal() {
            this.$emit('modal-close');
        },

        savePluginConfig() {
            // Save the plugin configuration using system config service
            const configData = {};
            Object.keys(this.pluginConfig).forEach(key => {
                if (key.startsWith('BrainstOdooPro.config.')) {
                    configData[key] = this.pluginConfig[key];
                }
            });

            return this.systemConfigApiService.saveValues(configData);
        },

        onSave() {
            this.isLoading = true;

            this.pluginConfig['BrainstOdooPro.config.enableTwoWaySync'] = this.enableTwoWaySync;

            // Store the sync order from date in plugin config
            if (this.syncOrderFromDate) {
                this.pluginConfig['BrainstOdooPro.config.syncOrderFromDate'] = this.syncOrderFromDate.toISOString().split('T')[0];
            }

            // Always remove existing automations first
            this.brainstOdooProApiService
                .removeAutomation()
                .then((response) => {
                    // Save config using system config service
                    return this.savePluginConfig();
                })
                .then((response) => {
                    if (this.enableTwoWaySync) {
                        return this.brainstOdooProApiService.createAutomation();
                    } else {
                        // If two-way sync is disabled, just show success and close
                        this.createNotificationSuccess({
                            title: this.$tc('brainst-odoo-pro.automation-modal.title'),
                            message: this.$tc('brainst-odoo-pro.automation-modal.success')
                        });
                        this.onCloseModal();
                        return Promise.resolve();
                    }
                })
                .then((response) => {
                    // This will only run if enableTwoWaySync is true
                    if (this.enableTwoWaySync && response) {
                        this.createNotificationSuccess({
                            title: this.$tc('brainst-odoo-pro.automation-modal.title'),
                            message: this.$tc('brainst-odoo-pro.automation-modal.success')
                        });
                        this.onCloseModal();
                    }
                })
                .catch((error) => {
                    let errorMessage = this.$tc('brainst-odoo-pro.automation-modal.error');

                    // Try to extract error message from response
                    if (error.response && error.response.data && error.response.data.message) {
                        errorMessage = error.response.data.message;
                    } else if (error.message) {
                        errorMessage = error.message;
                    }

                    this.createNotificationError({
                        title: this.$tc('brainst-odoo-pro.automation-modal.title'),
                        message: errorMessage
                    });
                })
                .finally(() => {
                    this.isLoading = false;
                });
        }
    }
});
