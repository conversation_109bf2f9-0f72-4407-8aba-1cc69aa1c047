#!/usr/bin/env python3
"""
Markdown to DOCX Converter for BrainstOdoopro Field Mapping Documentation
Converts the Markdown documentation to a well-formatted Word document.
"""

import re
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn

def add_table_style(doc):
    """Add a custom table style to the document"""
    try:
        # Try to get existing style or create new one
        table_style = doc.styles.add_style('CustomTable', WD_STYLE_TYPE.TABLE)
    except:
        # Style might already exist
        table_style = doc.styles['CustomTable']
    
    # Set table style properties
    table_style.base_style = doc.styles['Table Grid']
    return table_style

def parse_markdown_file(file_path):
    """Parse the markdown file and return structured content"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    sections = []
    current_section = None
    current_subsection = None
    lines = content.split('\n')
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        if line.startswith('# '):
            # Main title
            sections.append({
                'type': 'title',
                'text': line[2:].strip(),
                'level': 1
            })
        elif line.startswith('## '):
            # Section header
            current_section = {
                'type': 'heading',
                'text': line[3:].strip(),
                'level': 2,
                'content': []
            }
            sections.append(current_section)
        elif line.startswith('### '):
            # Subsection header
            current_subsection = {
                'type': 'heading',
                'text': line[4:].strip(),
                'level': 3,
                'content': []
            }
            if current_section:
                current_section['content'].append(current_subsection)
            else:
                sections.append(current_subsection)
        elif line.startswith('#### '):
            # Sub-subsection header
            subsubsection = {
                'type': 'heading',
                'text': line[5:].strip(),
                'level': 4,
                'content': []
            }
            if current_subsection:
                current_subsection['content'].append(subsubsection)
            elif current_section:
                current_section['content'].append(subsubsection)
            else:
                sections.append(subsubsection)
        elif line.startswith('| ') and '|' in line:
            # Table row
            table_data = []
            while i < len(lines) and lines[i].strip().startswith('|'):
                row = [cell.strip() for cell in lines[i].strip().split('|')[1:-1]]
                if not all(cell.startswith('-') for cell in row):  # Skip separator rows
                    table_data.append(row)
                i += 1
            i -= 1  # Adjust for the extra increment
            
            if table_data:
                table_item = {
                    'type': 'table',
                    'data': table_data
                }
                if current_subsection and 'content' in current_subsection:
                    current_subsection['content'].append(table_item)
                elif current_section and 'content' in current_section:
                    current_section['content'].append(table_item)
                else:
                    sections.append(table_item)
        elif line.startswith('- ') or line.startswith('* '):
            # List item
            list_items = []
            while i < len(lines) and (lines[i].strip().startswith('- ') or lines[i].strip().startswith('* ')):
                list_items.append(lines[i].strip()[2:])
                i += 1
            i -= 1  # Adjust for the extra increment
            
            list_item = {
                'type': 'list',
                'items': list_items
            }
            if current_subsection and 'content' in current_subsection:
                current_subsection['content'].append(list_item)
            elif current_section and 'content' in current_section:
                current_section['content'].append(list_item)
            else:
                sections.append(list_item)
        elif line and not line.startswith('---'):
            # Regular paragraph
            paragraph_text = line
            # Check for continuation lines
            j = i + 1
            while j < len(lines) and lines[j].strip() and not lines[j].startswith(('#', '|', '-', '*', '---')):
                paragraph_text += ' ' + lines[j].strip()
                j += 1
            i = j - 1  # Adjust index
            
            if paragraph_text:
                para_item = {
                    'type': 'paragraph',
                    'text': paragraph_text
                }
                if current_subsection and 'content' in current_subsection:
                    current_subsection['content'].append(para_item)
                elif current_section and 'content' in current_section:
                    current_section['content'].append(para_item)
                else:
                    sections.append(para_item)
        
        i += 1
    
    return sections

def add_content_to_doc(doc, sections):
    """Add parsed content to the Word document"""
    
    for section in sections:
        if section['type'] == 'title':
            # Main title
            title = doc.add_heading(section['text'], level=0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            title_run = title.runs[0]
            title_run.font.size = Pt(18)
            title_run.font.bold = True
            
        elif section['type'] == 'heading':
            # Add heading
            heading = doc.add_heading(section['text'], level=section['level'])
            heading_run = heading.runs[0]
            if section['level'] == 2:
                heading_run.font.size = Pt(14)
            elif section['level'] == 3:
                heading_run.font.size = Pt(12)
            elif section['level'] == 4:
                heading_run.font.size = Pt(11)
            
            # Add content if exists
            if 'content' in section:
                add_content_to_doc(doc, section['content'])
                
        elif section['type'] == 'paragraph':
            # Add paragraph
            para = doc.add_paragraph(section['text'])
            para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
            
        elif section['type'] == 'list':
            # Add list items
            for item in section['items']:
                para = doc.add_paragraph(item, style='List Bullet')
                
        elif section['type'] == 'table':
            # Add table
            if section['data']:
                table = doc.add_table(rows=len(section['data']), cols=len(section['data'][0]))
                table.style = 'Table Grid'
                
                # Add table data
                for i, row_data in enumerate(section['data']):
                    row = table.rows[i]
                    for j, cell_data in enumerate(row_data):
                        if j < len(row.cells):
                            cell = row.cells[j]
                            cell.text = cell_data
                            # Make header row bold
                            if i == 0:
                                for paragraph in cell.paragraphs:
                                    for run in paragraph.runs:
                                        run.font.bold = True
                
                # Add spacing after table
                doc.add_paragraph()

def main():
    """Main function to convert MD to DOCX"""
    input_file = 'BrainstOdoopro_Field_Mapping_Documentation.md'
    output_file = 'BrainstOdoopro_Field_Mapping_Documentation.docx'
    
    print(f"Converting {input_file} to {output_file}...")
    
    # Parse markdown file
    sections = parse_markdown_file(input_file)
    
    # Create new document
    doc = Document()
    
    # Set document margins
    sections_obj = doc.sections
    for section in sections_obj:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)
    
    # Add custom table style
    add_table_style(doc)
    
    # Add content to document
    add_content_to_doc(doc, sections)
    
    # Save document
    doc.save(output_file)
    print(f"Successfully created {output_file}")

if __name__ == '__main__':
    main()
